"""
Simplified Followup Generator for Socket Service V2
Clean, simple followup generation without complex logic.
"""

import base64
import os
from google import genai
from google.genai import types
from bson import ObjectId
import json
from datetime import datetime, timezone
import asyncio

from app.shared.db_enums import TaskStatus, QuizType, InputType, GenerationType, CollectionName
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB

logger = setup_new_logging(__name__)


async def followup_generate(task_set_id: str, current_user: UserTenantDB) -> None:
    """
    SIMPLIFIED followup generation - clean and efficient.
    
    Args:
        task_set_id: The completed task set ID
        current_user: Current user context
    """
    try:
        logger.info(f"🚀 SIMPLIFIED FOLLOWUP GENERATION: Task set {task_set_id}")
        
        # Get simple config - max followup count (default 3)
        max_followup_count = _get_followup_config(current_user)
        
        # Get the completed task set
        task_set = await current_user.async_db.task_sets.find_one(
            {"_id": ObjectId(task_set_id)},
            {"tasks": 1, "session_id": 1, "followup_count": 1, "input_content": 1, "original_task_set_id": 1}
        )

        if not task_set:
            logger.error(f"Task set {task_set_id} not found")
            return

        # Check followup limits
        current_followup_count = task_set.get("followup_count", 0)
        if current_followup_count >= max_followup_count:
            logger.info(f"🛑 Max followup depth reached ({current_followup_count}/{max_followup_count})")
            return

        # Check for existing followup at next level
        next_level = current_followup_count + 1
        original_id = task_set.get("original_task_set_id", ObjectId(task_set_id))
        
        existing = await current_user.async_db.task_sets.find_one({
            "original_task_set_id": original_id,
            "followup_count": next_level
        })
        
        if existing:
            logger.info(f"🔄 Followup level {next_level} already exists")
            return

        # Get original audio content from the PRIMARY task set (not current completed task set)
        audio_content = await _get_original_audio_content(current_user, original_id)
        if not audio_content:
            logger.error("No audio content found for followup generation")
            return

        # Get previous questions for context
        previous_questions = await _get_previous_questions(current_user, original_id, current_followup_count)

        # Generate followup tasks using simplified AI call
        followup_tasks = await _generate_followup_tasks_simple(
            current_user, audio_content, previous_questions, next_level
        )
        followup_tasks = followup_tasks.get("tasks")

        logger.debug(f"Generated followup tasks type: {type(followup_tasks)}")
        logger.debug(f"Generated followup tasks: {followup_tasks}")
        logger.debug(f"Generated followup tasks keys: {followup_tasks.keys()}")
        # title
        logger.debug(f"Generated followup tasks title: {followup_tasks.get('title')}")
        logger.debug(f"Generated followup tasks usage: {followup_tasks.get('thumbnail')}") 
        logger.debug(f"Generated followup tasks tasks: {followup_tasks.get('tasks')}")

        # raise Exception("Test error")
        if not followup_tasks or not followup_tasks.get("tasks"):
            logger.error("Failed to generate followup tasks")
            return

        # Save followup tasks to database
        await _save_followup_tasks_simple(
            current_user, followup_tasks, task_set, original_id, next_level
        )

        logger.info(f"✅ SIMPLIFIED FOLLOWUP GENERATION COMPLETED: Level {next_level}")

    except Exception as e:
        logger.error(f"❌ Simplified followup generation failed: {e}")


def _get_followup_config(current_user: UserTenantDB) -> int:
    """Get simple followup configuration."""
    try:
        config_collection = current_user.db[CollectionName.CONFIG]
        config_doc = config_collection.find_one({"name": "media_generation"})
        return config_doc.get("followup_generator_count", 3) if config_doc else 3
    except:
        return 3  # Default to 3 levels


async def _get_original_audio_content(current_user: UserTenantDB, original_task_set_id: ObjectId) -> bytes:
    """Get original audio content from the PRIMARY task set for followup generation."""
    try:
        logger.info(f"🔍 Looking for original task set: {original_task_set_id}")

        # Get the ORIGINAL primary task set (not the current completed followup)
        original_task_set = await current_user.async_db.task_sets.find_one(
            {"_id": original_task_set_id},
            {"input_content": 1, "gentype": 1, "title": 1}
        )

        if not original_task_set:
            logger.error(f"❌ Original task set {original_task_set_id} not found in database")
            return None

        logger.info(f"✅ Found original task set: {original_task_set.get('title')} (gentype: {original_task_set.get('gentype')})")

        # Get input content path from the ORIGINAL task set
        input_content = original_task_set.get("input_content")
        if not input_content:
            logger.error(f"❌ No input_content found in original task set {original_task_set_id}")
            return None

        logger.info(f"🎵 Found input_content: {input_content}")

        # Download audio from MinIO using the correct method
        if current_user.minio:
            logger.info(f"📥 Downloading audio from MinIO: {input_content}")
            audio_data = current_user.minio.get_audio_bytes(
                object_name=input_content.get("object_name")
            )
            if audio_data:
                logger.info(f"✅ Retrieved original audio content: {input_content} ({len(audio_data)} bytes)")
                return audio_data
            else:
                logger.error(f"❌ No audio data returned from MinIO for: {input_content}")
                return None
        else:
            logger.error("❌ No MinIO client available")
            return None
    except Exception as e:
        logger.error(f"❌ Failed to get original audio content: {e}")
        import traceback
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        return None


async def _get_previous_questions(current_user: UserTenantDB, original_id: ObjectId, current_level: int) -> str:
    """Get previous questions and answers organized by level for context."""
    try:
        # Get all task sets to include (original + all previous followup levels)
        all_task_sets_to_include = []

        # Always include the original task set
        original_task_set = await current_user.async_db.task_sets.find_one(
            {"_id": original_id},
            {"tasks": 1, "followup_count": 1}
        )

        if original_task_set:
            all_task_sets_to_include.append({
                "tasks": original_task_set.get("tasks", []),
                "level": "Original"
            })

        # Include all previous followup levels (1 to current_level)
        for level in range(1, current_level + 1):
            followup_task_set = await current_user.async_db.task_sets.find_one(
                {
                    "original_task_set_id": original_id,
                    "followup_count": level
                },
                {"tasks": 1}
            )
            if followup_task_set:
                all_task_sets_to_include.append({
                    "tasks": followup_task_set.get("tasks", []),
                    "level": f"Level {level}"
                })

        # Collect all task IDs from all levels
        all_task_ids = []
        for task_set_info in all_task_sets_to_include:
            all_task_ids.extend([ObjectId(tid) for tid in task_set_info["tasks"]])

        # Get all task items from all levels
        task_items = await current_user.async_db.task_items.find(
            {"_id": {"$in": all_task_ids}},
            {"question": 1, "correct_answer": 1, "_id": 1}
        ).to_list(length=None)

        # Create a mapping of task_id to task_item for organizing by level
        task_items_map = {str(task["_id"]): task for task in task_items}

        # Build previous tasks string organized by level for context
        prev_tasks = ""
        for task_set_info in all_task_sets_to_include:
            prev_tasks += f"\n=== {task_set_info['level']} Questions ===\n"

            for task_id in task_set_info["tasks"]:
                task = task_items_map.get(task_id)
                if task:
                    question = task.get("question", {})
                    correct_answer = task.get("correct_answer", {})

                    # Clean up question data
                    question_copy = question.copy()
                    question_copy.pop("media_url", None)
                    question_copy.pop("metadata", None)

                    prev_tasks += f"Question: {question_copy}\nAnswer: {correct_answer}\n\n"

        logger.info(f"📋 Built context from {len(all_task_sets_to_include)} levels with {len(task_items)} total questions")
        return prev_tasks

    except Exception as e:
        logger.error(f"Failed to get previous questions: {e}")
        return ""


async def _generate_followup_tasks_simple(
    current_user: UserTenantDB,
    audio_content: bytes,
    previous_questions: str,
    level: int
) -> dict:
    """Generate followup tasks using simplified AI call."""
    try:
        # Use the formatted previous questions string directly
        previous_tasks = previous_questions

        prompt = """
You are a Nepali language tutor designing interactive quiz tasks for **8-year-old children**.
### Instructions:
You are given a **Nepali audio recording**.
Your task is to generate exactly **4 followup quiz questions** based on what is said in the audio and what has been generated previously.
Generate augmented questions based on the previous tasks, their questions and options.
Do not repeat the same questions or options.

Previous Tasks with Questions and Options:
```{}```""".format(previous_tasks) + """

### Question Format:

Each task must follow this question-type cycle:

1. "single\_choice"
2. "multiple\_choice"
3. "image\_identification"
4. "speak\_word"

(Repeat this order if `num_tasks` > 4)

### Quiz Question Structure:

```json
"output": {{
  "title": "title for the whole task set",
  "thumbnail": "thumbnail title for image generation",
  "tasks": [
    {{
      "type": "single_choice",
      "text": "Question in Nepali",
      "translated_text": "English translation",
      "options": {
        "a": "घोड़ा",
        "b": "कुकुर",
        "c": "हात्ती",
        "d": "फल"
      },
      "answer_hint": "elephant",
      "answer": "a"
    }}
  ]
}}

```

### Guidelines:

* Use simple, vivid Nepali suited for children.
* All content must be directly grounded in the audio.
* For "speak\_word":

  * Only one Nepali word (no phrases or sentences).
* For "image\_identification":

  * Ask general visual questions, e.g., “What is seen in the picture?”
  * Do not name the object directly in the question.
* Use single-word answer options (no phrases).
* "answer\_hint" must be:

  * In English for "single\_choice", "multiple\_choice", and "image\_identification".
  * In Nepali (one word only) for "speak\_word".
"""

        # Use Gemini to generate followup tasks (matching original followup.py structure)
        client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))

        model = "gemini-2.0-flash"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_bytes(
                        mime_type="audio/mp4",
                        data=audio_content
                    ),
                ],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            temperature=0,
            response_mime_type="application/json",
            system_instruction=[
                types.Part.from_text(text=prompt),
            ],
        )

        # Generate content stream (matching original followup.py)
        output = ""

        response = client.models.generate_content(
            model=model,
            contents=contents,
            config=generate_content_config,
        )

        # Parse and return result (matching original followup.py structure)
        try:
            response_data = json.loads(response.text)

            # Debug logging to see the response structure
            logger.debug(f"Gemini response structure: {list(response_data.keys()) if isinstance(response_data, dict) else type(response_data)}")

            # Extract tasks from the "output" structure (matching prompt format)
            if "output" in response_data:
                output_data = response_data["output"]
                tasks = output_data.get("tasks", [])
                title = output_data.get("title", "Followup Tasks")
                thumbnail = output_data.get("thumbnail", "")
                logger.debug(f"Found 'output' structure with {len(tasks)} tasks")
            else:
                # Fallback: assume response_data is the tasks directly
                tasks = response_data.get("tasks", response_data if isinstance(response_data, list) else [])
                title = response_data.get("title", "Followup Tasks")
                thumbnail = response_data.get("thumbnail", "")
                logger.debug(f"Using fallback parsing, found {len(tasks)} tasks")

            # Debug log first task structure if available
            if tasks and len(tasks) > 0:
                logger.debug(f"First task structure: {list(tasks[0].keys()) if isinstance(tasks[0], dict) else type(tasks[0])}")

            logger.info(f"✅ Generated {len(tasks)} followup tasks with title: {title}")
            return {
                "tasks": tasks,
                "title": title,
                "thumbnail": thumbnail,
                "usage": response.usage_metadata.model_dump(),
            }
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse Gemini response: {e}")
            raise e
        
    except Exception as e:
        logger.error(f"Failed to generate followup tasks: {e}")
        return {"tasks": [], "usage": {}}


async def _save_followup_tasks_simple(
    current_user: UserTenantDB,
    followup_data: dict,  # Now expects full data with title and tasks
    parent_task_set: dict,
    original_id: ObjectId,
    level: int
):
    """Save followup tasks to database (simplified)."""
    try:
        # Create followup task set
        followup_set_id = ObjectId()

        # Extract tasks from Gemini response (matching original followup.py format)
        tasks = followup_data.get("tasks", [])
        title= followup_data.get("title")
        thumbnail = followup_data.get("thumbnail")
        if not title:
            logger.error("No title found in followup data")
        else:
            title=f"{title} (Level {level})"

        followup_set_doc = {
            "_id": followup_set_id,
            "session_id": parent_task_set.get("session_id"),
            "user_id": ObjectId(current_user.user.id),
            "title": title,  # Use title from Gemini
            "thumbnail": thumbnail,
            "difficulty_level": "medium",
            "input_content": parent_task_set.get("input_content"),
            "gentype": GenerationType.FOLLOW_UP.value,
            "status": TaskStatus.PENDING.value,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "tasks": [],  # List of string IDs
            "stories": [],  # List of string IDs - Followup tasks don't typically have stories, but keep structure consistent
            "followup_count": level,
            "original_task_set_id": original_id,
            "parent_task_set_id": ObjectId(parent_task_set["_id"]),
            "total_tasks": len(tasks),
            "total_stories": 0,
            "attempted_tasks": 0,
            "total_score": len(tasks) * 10,
            "scored": 0
        }

        # Create task items
        task_items = []
        for i, task in enumerate(tasks):
            task_id = ObjectId()
            
            # Map task type to database enum
            task_type = task.get("type", "single_choice")
            if task_type == "single_choice":
                quiz_type = QuizType.SINGLE_CHOICE.value
            elif task_type == "multiple_choice":
                quiz_type = QuizType.MULTIPLE_CHOICE.value
            elif task_type == "image_identification":
                quiz_type = QuizType.IMAGE_IDENTIFICATION.value
            elif task_type == "speak_word":
                quiz_type = QuizType.SPEAK_WORD.value
            else:
                quiz_type = QuizType.SINGLE_CHOICE.value

            # Build standardized question structure (matching primary task format)
            question_data = {
                "type": task_type,
                "text": task.get("text", ""),
                "translated_text": task.get("translated_text", ""),
                "options": task.get("options", {}),
                "answer_hint": task.get("answer_hint", ""),
                "options_metadata": {},  # Will be populated during options audio generation
                "image_metadata": {},    # Will be populated during image generation
                "audio_metadata": {},    # Will be populated during audio generation
                "metadata": {
                }
            }

            # Build standardized correct answer structure with proper value types
            # Extract answer from task - it should be in the task directly
            raw_answer = task.get("answer", "")

            # Debug logging to see what we're getting
            logger.debug(f"Task {i+1}: type={task_type}, raw_answer='{raw_answer}', task_keys={list(task.keys())}")

            if task_type in ["single_choice", "speak_word", "image_identification"]:
                correct_answer_data = {
                    "type": "single",
                    "value": raw_answer  # Single value as string (e.g., "a")
                }
            else:  # multiple_choice
                # Handle multiple choice answers - convert string to array if needed
                if isinstance(raw_answer, str) and raw_answer:
                    # If Gemini returns "a,b" convert to ["a", "b"]
                    if "," in raw_answer:
                        answer_array = [item.strip() for item in raw_answer.split(",")]
                    else:
                        # If single answer like "a", convert to ["a"]
                        answer_array = [raw_answer.strip()]
                elif isinstance(raw_answer, list):
                    # If already an array, use as-is
                    answer_array = raw_answer
                else:
                    # Fallback for empty or invalid answers
                    logger.warning(f"Empty or invalid answer for multiple choice task {i+1}: {raw_answer}")
                    answer_array = []

                correct_answer_data = {
                    "type": "multiple",
                    "value": answer_array  # Array of values (e.g., ["a", "b"])
                }

            task_item = {
                "_id": task_id,
                "task_set_id": followup_set_id,
                "user_id": ObjectId(current_user.user.id),
                "title": task.get("title", f"Followup Task {i+1}"),
                "question": question_data,
                "correct_answer": correct_answer_data,
                "user_answer": None,
                "type": quiz_type,
                "input_type": InputType.AUDIO.value,
                "status": TaskStatus.PENDING.value,
                "result": None,
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "submitted_at": None,
                "attempts_count": 0,
                "difficulty_level": 1,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "image_metadata": {},  # Top level for image tasks
                "audio_metadata": {},  # Top level for audio tasks
                "metadata": {
                    "_priority": i,
                    "_media_ready": False,
                    "_options_audio_ready": False,
                    "status": "generating"  # Universal root status - default to generating
                }
            }
            
            task_items.append(task_item)
            followup_set_doc["tasks"].append(str(task_id))  # Convert ObjectId to string

        # Save to database
        await current_user.async_db.task_sets.insert_one(followup_set_doc)
        await current_user.async_db.task_items.insert_many(task_items)

        # Mark followup level as completed in original task set for tracking
        await _mark_followup_level_completed(current_user, original_id, level)

        logger.info(f"✅ Saved {len(task_items)} followup tasks for level {level}")

        # Generate media for followup tasks (matching primary task generation pattern)
        await _generate_followup_media_sequential(current_user, task_items)

        logger.info(f"🎬 Completed media generation for {len(task_items)} followup tasks")

    except Exception as e:
        logger.error(f"Failed to save followup tasks: {e}")


async def _mark_followup_level_completed(
    current_user: UserTenantDB,
    original_task_set_id: ObjectId,
    level: int
):
    """Mark followup level as completed in original task set for tracking (PRESERVED LOGIC)."""
    try:
        level_key = str(level)

        await current_user.async_db.task_sets.update_one(
            {"_id": original_task_set_id},
            {
                "$set": {
                    f"followup_levels_completed.{level_key}": True,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        logger.info(f"✅ FOLLOWUP TRACKING: Marked level {level} as completed in original task set {original_task_set_id}")

    except Exception as e:
        logger.error(f"❌ Failed to mark followup level completed: {e}")


async def _generate_followup_media_sequential(current_user: UserTenantDB, task_items: list):
    """Generate media for followup tasks sequentially (matching primary task generation pattern)."""
    try:
        logger.info(f"🎬 Starting media generation for {len(task_items)} followup tasks")

        for i, task_item in enumerate(task_items, 1):
            task_id = task_item["_id"]
            task_type = task_item.get("type")

            logger.info(f"🎯 FOLLOWUP TASK {i}/{len(task_items)} | ID: {task_id} | TYPE: {task_type}")

            try:
                # Generate media based on task type (matching primary task generation)
                if task_type == QuizType.SINGLE_CHOICE.value:
                    await _process_followup_single_choice(current_user, task_item, task_id)
                elif task_type == QuizType.MULTIPLE_CHOICE.value:
                    await _process_followup_multiple_choice(current_user, task_item, task_id)
                elif task_type == QuizType.IMAGE_IDENTIFICATION.value:
                    await _process_followup_image_identification(current_user, task_item, task_id)
                elif task_type == QuizType.SPEAK_WORD.value:
                    await _process_followup_speak_word(current_user, task_item, task_id)
                else:
                    logger.warning(f"⚠️  Unknown task type: {task_type} for task {task_id}")

                logger.info(f"✅ FOLLOWUP TASK {i}/{len(task_items)} | COMPLETED")

            except Exception as task_error:
                logger.error(f"❌ FOLLOWUP TASK {i}/{len(task_items)} | FAILED: {task_error}")
                continue

        logger.info(f"🎬 Completed media generation for all followup tasks")

    except Exception as e:
        logger.error(f"❌ Failed to generate followup media: {e}")


async def _process_followup_single_choice(current_user: UserTenantDB, task: dict, task_id: ObjectId):
    """Process single choice followup task (generate options audio)."""
    try:
        # Set status to generating at start
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata.options_audio_status": "generating",
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Import the function from task_utils_v2
        from app.v2.api.socket_service_v2.generator.task_utils_v2 import _generate_options_audio_sequential

        # Generate options audio sequentially (matching primary task generation)
        if task.get("question", {}).get("options"):
            await _generate_options_audio_sequential(current_user, task, task_id, None)

        # Set status to completed on success
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata.options_audio_status": "completed",
                    "metadata.status": "completed",  # Universal status
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Import and call the thumbnail generation check directly
        from app.v2.api.socket_service_v2.generator.task_utils_v2 import _check_and_generate_thumbnail
        await _check_and_generate_thumbnail(current_user, task_id)

        logger.info(f"✅ Single choice followup task {task_id} completed with options audio")

    except Exception as e:
        # Set status to failed on error
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata.options_audio_status": "failed",
                    "metadata.options_audio_error": str(e),
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )
        logger.error(f"❌ Failed to process single choice followup task {task_id}: {e}")
        raise


async def _process_followup_multiple_choice(current_user: UserTenantDB, task: dict, task_id: ObjectId):
    """Process multiple choice followup task (generate options audio)."""
    try:
        # Import the function from task_utils_v2
        from app.v2.api.socket_service_v2.generator.task_utils_v2 import _generate_options_audio_sequential

        # Generate options audio sequentially (matching primary task generation)
        if task.get("question", {}).get("options"):
            await _generate_options_audio_sequential(current_user, task, task_id, None)

        # Set status to completed on success
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata.options_audio_status": "completed",
                    "metadata.status": "completed",  # Universal status
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Import and call the thumbnail generation check directly
        from app.v2.api.socket_service_v2.generator.task_utils_v2 import _check_and_generate_thumbnail
        await _check_and_generate_thumbnail(current_user, task_id)

        logger.info(f"✅ Multiple choice followup task {task_id} completed with options audio")

    except Exception as e:
        logger.error(f"❌ Failed to process multiple choice followup task {task_id}: {e}")
        raise


async def _process_followup_image_identification(current_user: UserTenantDB, task: dict, task_id: ObjectId):
    """Process image identification followup task (generate image + options audio)."""
    try:
        # Import the functions from task_utils_v2
        from app.v2.api.socket_service_v2.generator.task_utils_v2 import _generate_task_media, _generate_options_audio_sequential

        # Generate task media (image) first (matching primary task generation)
        await _generate_task_media(current_user, task, task_id, None)

        # Generate options audio sequentially (matching primary task generation)
        if task.get("question", {}).get("options"):
            await _generate_options_audio_sequential(current_user, task, task_id, None)

        # Set status to completed after all media generation
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "metadata.status": "completed",  # Universal status
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Import and call the thumbnail generation check directly
        from app.v2.api.socket_service_v2.generator.task_utils_v2 import _check_and_generate_thumbnail
        await _check_and_generate_thumbnail(current_user, task_id)

        logger.info(f"✅ Image identification followup task {task_id} completed with image and options audio")

    except Exception as e:
        logger.error(f"❌ Failed to process image identification followup task {task_id}: {e}")
        raise


async def _process_followup_speak_word(current_user: UserTenantDB, task: dict, task_id: ObjectId):
    """Process speak word followup task (generate audio)."""
    try:
        # Import the function from task_utils_v2
        from app.v2.api.socket_service_v2.generator.task_utils_v2 import _generate_task_media

        # Generate task media (audio) (matching primary task generation)
        await _generate_task_media(current_user, task, task_id, None)

        # Import and call the completion status function to trigger thumbnail generation
        from app.v2.api.socket_service_v2.generator.task_utils_v2 import _update_task_generation_completion_status
        await _update_task_generation_completion_status(current_user, task_id, "speak_word")

        logger.info(f"✅ Speak word followup task {task_id} completed with audio")

    except Exception as e:
        logger.error(f"❌ Failed to process speak word followup task {task_id}: {e}")
        raise
